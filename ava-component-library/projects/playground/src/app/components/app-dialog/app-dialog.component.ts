import { Component } from '@angular/core';
import { DialogService, DialogButton } from '../../../../../play-comp-library/src/lib/components/dialog/dialog-service';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';
import { CommonModule } from '@angular/common';

interface DialogExample {
  title: string;
  description: string;
  buttonLabel: string;
  buttonVariant: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
  action: () => void;
}

@Component({
  selector: 'app-app-dialog',
  imports: [ButtonComponent, CommonModule],
  providers: [DialogService],
  templateUrl: './app-dialog.component.html',
  styleUrl: './app-dialog.component.scss'
})
export class AppDialogComponent {
  constructor(private dialogService: DialogService) { }

  dialogExamples: DialogExample[] = [
    {
      title: 'Success Dialog',
      description: 'Shows a success message with a green checkmark icon',
      buttonLabel: 'Show Success',
      buttonVariant: 'success',
      action: () => this.showSuccess()
    },
    {
      title: 'Error Dialog',
      description: 'Displays error messages with a red alert icon',
      buttonLabel: 'Show Error',
      buttonVariant: 'danger',
      action: () => this.showError()
    },
    {
      title: 'Warning Dialog',
      description: 'Shows warning messages with a yellow triangle icon',
      buttonLabel: 'Show Warning',
      buttonVariant: 'warning',
      action: () => this.showWarning()
    },
    {
      title: 'Info Dialog',
      description: 'Displays informational messages with a blue info icon',
      buttonLabel: 'Show Info',
      buttonVariant: 'primary',
      action: () => this.showInfo()
    },
    {
      title: 'Confirmation Dialog',
      description: 'Asks for user confirmation with action buttons',
      buttonLabel: 'Show Confirmation',
      buttonVariant: 'secondary',
      action: () => this.showConfirmation()
    },
    {
      title: 'Loading Dialog',
      description: 'Shows loading state with spinner and progress',
      buttonLabel: 'Show Loading',
      buttonVariant: 'secondary',
      action: () => this.showLoading()
    },
    {
      title: 'Custom Dialog',
      description: 'Flexible dialog with custom content and buttons',
      buttonLabel: 'Show Custom',
      buttonVariant: 'primary',
      action: () => this.showCustom()
    }
  ];

  showSuccess() {
    this.dialogService.success({
      title: 'Message Sent Successfully!',
      message: 'Thank you for contacting us. We will get back to you soon.'
    }).then(result => {
      console.log('Success dialog closed:', result);
    });
  }

  showError() {
    this.dialogService.error({
      title: 'Connection Failed',
      message: 'Unable to connect to the server. Please check your internet connection and try again.',
      showRetryButton: true,
      retryButtonText: 'Retry'
    }).then(result => {
      console.log('Error dialog closed:', result);

      // Handle different button actions
      if (result.action === 'retry') {
        alert('User clicked Retry! You can implement retry logic here.');
        // Example: this.retryConnection();
      } else if (result.action === 'close') {
        alert('User clicked Close! You can implement error handling here.');
        // Example: this.handleConnectionError();
      }
    });
  }

  showWarning() {
    this.dialogService.warning({
      title: 'Unsaved Changes',
      message: 'You have unsaved changes that will be lost if you continue. Are you sure you want to proceed?',
      showProceedButton: true,
      proceedButtonText: 'Discard Changes'
    }).then(result => {
      console.log('Warning dialog closed:', result);

      // Handle different button actions
      if (result.action === 'proceed') {
        alert('User chose to discard changes! You can implement navigation here.');
        // Example: this.discardChangesAndNavigate();
      } else if (result.action === 'cancel') {
        alert('User chose to keep changes! You can stay on current page.');
        // Example: this.stayOnCurrentPage();
      }
    });
  }

  showInfo() {
    this.dialogService.info({
      title: 'New Feature Available',
      message: 'We have added new features to improve your experience. Would you like to learn more?',
      showLearnMoreButton: true,
      learnMoreButtonText: 'Learn More'
    }).then(result => {
      console.log('Info dialog closed:', result);
    });
  }

  showConfirmation() {
    this.dialogService.confirmation({
      title: 'Delete Account',
      message: 'This action cannot be undone. All your data will be permanently deleted.',
      confirmButtonText: 'Delete Account',
      cancelButtonText: 'Keep Account',
      destructive: true
    }).then(result => {
      console.log('Confirmation dialog closed:', result);

      // Handle confirmation result
      if (result.confirmed === true) {
        alert('User confirmed deletion! You can implement delete logic here.');
        // Example: this.deleteUserAccount();
      } else if (result.confirmed === false) {
        alert('User cancelled deletion! Account is safe.');
        // Example: this.cancelDeletion();
      }
    });
  }

  showLoading() {
    const loadingDialog = this.dialogService.loading({
      title: 'Processing...',
      message: 'Please wait while we process your request.',
      showProgress: true,
      showCancelButton: true
    });

    // Simulate progress
    let progress = 0;
    const interval = setInterval(() => {
      progress += 10;
      if (progress >= 100) {
        clearInterval(interval);
        this.dialogService.close();
      }
    }, 500);

    loadingDialog.then(result => {
      clearInterval(interval);
      console.log('Loading dialog closed:', result);
    });
  }

  showCustom() {
    const customButtons: DialogButton[] = [
      { label: 'Cancel', variant: 'secondary', action: 'cancel' },
      { label: 'Save Draft', variant: 'secondary', action: 'draft' },
      { label: 'Publish', variant: 'primary', action: 'publish' }
    ];

    this.dialogService.custom({
      title: 'Publish Article',
      message: 'Your article is ready to be published. You can also save it as a draft for later.',
      variant: 'info',
      buttons: customButtons
    }).then(result => {
      console.log('Custom dialog closed:', result);

      // Handle custom button actions
      switch (result.action) {
        case 'publish':
          alert('User clicked Publish! You can implement publish logic here.');
          // Example: this.publishArticle();
          break;
        case 'draft':
          alert('User clicked Save Draft! You can implement draft saving here.');
          // Example: this.saveAsDraft();
          break;
        case 'cancel':
          alert('User clicked Cancel! You can return to editor.');
          // Example: this.returnToEditor();
          break;
        default:
          console.log('Unknown action:', result.action);
      }
    });
  }

  // Example business logic methods that users would implement
  private retryConnection() {
    console.log('🔄 Retrying connection...');
    // Implement actual retry logic here
  }

  private handleConnectionError() {
    console.log('❌ Handling connection error...');
    // Implement error handling logic here
  }

  private discardChangesAndNavigate() {
    console.log('🗑️ Discarding changes and navigating...');
    // Implement navigation logic here
  }

  private stayOnCurrentPage() {
    console.log('📄 Staying on current page...');
    // Keep user on current page
  }

  private deleteUserAccount() {
    console.log('🗑️ Deleting user account...');
    // Implement account deletion logic here
  }

  private cancelDeletion() {
    console.log('✅ Account deletion cancelled...');
    // Handle cancellation logic here
  }

  private publishArticle() {
    console.log('📝 Publishing article...');
    // Implement article publishing logic here
  }

  private saveAsDraft() {
    console.log('💾 Saving as draft...');
    // Implement draft saving logic here
  }

  private returnToEditor() {
    console.log('✏️ Returning to editor...');
    // Return to article editor
  }
}
