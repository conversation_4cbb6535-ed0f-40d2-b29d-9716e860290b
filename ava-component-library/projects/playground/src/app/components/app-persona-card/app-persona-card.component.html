<div class="persona-card-demo">
  <div class="demo-header">
    <h2>Persona Card Component</h2>
    <p>
      A specialized card component for displaying user personas with decorative elements,
      personal information, quotes, and personality traits.
    </p>
  </div>

  <!-- Persona Selection -->
  <div class="persona-selection-card">
    <ava-card>
      <div header>
        <h3>Select Persona</h3>
      </div>
      <div content>
        <div class="persona-tabs">
          <ava-button
            *ngFor="let persona of personas; let i = index"
            (click)="selectPersona(i)"
            [variant]="selectedPersonaIndex === i ? 'primary' : 'secondary'"
            size="small"
            [label]="persona.name"
            [iconName]="'user'"
            [iconSize]="14"
          >
          </ava-button>
        </div>
      </div>
    </ava-card>
  </div>

  <!-- Configuration Controls -->
  <div class="controls-card">
    <ava-card>
      <div header>
        <h3>Configuration Options</h3>
      </div>
      <div content>
        <div class="controls-grid">
          <!-- Display Options -->
          <div class="control-group">
            <h4>Display Options</h4>
            <div class="control-buttons">
              <ava-button
                [label]="config.showCornerNumbers ? 'Hide Corner Numbers' : 'Show Corner Numbers'"
                variant="secondary"
                size="small"
                (click)="toggleCornerNumbers()"
              >
              </ava-button>
              <ava-button
                [label]="config.showQuote ? 'Hide Quote' : 'Show Quote'"
                variant="secondary"
                size="small"
                (click)="toggleQuote()"
              >
              </ava-button>
              <ava-button
                [label]="config.showPersonality ? 'Hide Personality' : 'Show Personality'"
                variant="secondary"
                size="small"
                (click)="togglePersonality()"
              >
              </ava-button>
            </div>
          </div>

          <!-- Color Options -->
          <div class="control-group">
            <h4>Accent Colors</h4>
            <div class="color-buttons">
              <ava-button
                label="Blue"
                variant="primary"
                size="small"
                (click)="changeAccentColor('#2196F3')"
              >
              </ava-button>
              <ava-button
                label="Purple"
                variant="secondary"
                size="small"
                (click)="changeAccentColor('#9C27B0')"
              >
              </ava-button>
              <ava-button
                label="Green"
                variant="secondary"
                size="small"
                (click)="changeAccentColor('#4CAF50')"
              >
              </ava-button>
              <ava-button
                label="Orange"
                variant="secondary"
                size="small"
                (click)="changeAccentColor('#FF9800')"
              >
              </ava-button>
            </div>
          </div>

          <!-- Corner Number Options -->
          <div class="control-group">
            <h4>Corner Numbers</h4>
            <div class="number-buttons">
              <ava-button
                label="24"
                variant="secondary"
                size="small"
                (click)="changeCornerNumber('24')"
              >
              </ava-button>
              <ava-button
                label="01"
                variant="secondary"
                size="small"
                (click)="changeCornerNumber('01')"
              >
              </ava-button>
              <ava-button
                label="42"
                variant="secondary"
                size="small"
                (click)="changeCornerNumber('42')"
              >
              </ava-button>
            </div>
          </div>
        </div>
      </div>
    </ava-card>
  </div>

  <!-- Persona Card Demo -->
  <div class="demo-section">
    <ava-card>
      <div header>
        <h3>Interactive Persona Card</h3>
        <p>
          Click on the avatar or interact with the card to see events
        </p>
      </div>
      <div content>
        <div class="card-showcase">
          <ava-persona-card
            [profile]="currentPersona"
            [config]="config"
            (profileClick)="onProfileClick($event)"
            (avatarClick)="onAvatarClick($event)"
          >
          </ava-persona-card>
        </div>
      </div>
    </ava-card>
  </div>
</div>
