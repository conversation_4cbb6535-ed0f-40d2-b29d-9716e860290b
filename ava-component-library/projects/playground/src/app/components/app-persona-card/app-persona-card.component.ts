import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { 
  PersonaCardComponent, 
  PersonaProfile, 
  PersonaConfig 
} from '../../../../../play-comp-library/src/lib/composite-components/persona-card/persona-card.component';
import { CardComponent } from '../../../../../play-comp-library/src/lib/components/card/card.component';
import { ButtonComponent } from '../../../../../play-comp-library/src/lib/components/button/button.component';

@Component({
  selector: 'ava-persona-card-demo',
  standalone: true,
  imports: [
    CommonModule,
    PersonaCardComponent,
    CardComponent,
    ButtonComponent
  ],
  templateUrl: './app-persona-card.component.html',
  styleUrl: './app-persona-card.component.scss'
})
export class AppPersonaCardComponent {
  selectedPersonaIndex = 0;

  // Sample persona profiles
  personas: <PERSON>aProfile[] = [
    {
      id: '1',
      name: '<PERSON>',
      title: 'Sales Manager',
      avatar: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
      age: 38,
      education: 'MBA',
      status: 'Married',
      location: 'Mumbai',
      techLiteracy: 'Medium',
      quote: 'I am used to online service and I usually do my online shopping from Instagram.',
      personality: ['Introvert', 'Thinker', 'Spender']
    },
    {
      id: '2',
      name: 'Michael Chen',
      title: 'Product Designer',
      avatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
      age: 29,
      education: 'Bachelor\'s',
      status: 'Single',
      location: 'San Francisco',
      techLiteracy: 'High',
      quote: 'Design is not just what it looks like and feels like. Design is how it works.',
      personality: ['Extrovert', 'Creative', 'Saver']
    },
    {
      id: '3',
      name: 'Emma Rodriguez',
      title: 'Marketing Director',
      avatar: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
      age: 42,
      education: 'Master\'s',
      status: 'Married',
      location: 'Barcelona',
      techLiteracy: 'High',
      quote: 'Marketing is no longer about the stuff that you make, but about the stories you tell.',
      personality: ['Ambivert', 'Strategist', 'Investor']
    }
  ];

  // Configuration options
  config: PersonaConfig = {
    showCornerNumbers: true,
    cornerNumber: '24',
    showQuote: true,
    showPersonality: true,
    accentColor: '#2196F3'
  };

  get currentPersona(): PersonaProfile {
    return this.personas[this.selectedPersonaIndex];
  }

  selectPersona(index: number) {
    this.selectedPersonaIndex = index;
  }

  onProfileClick(personaId: string) {
    console.log('Profile clicked:', personaId);
  }

  onAvatarClick(personaId: string) {
    console.log('Avatar clicked:', personaId);
  }

  // Configuration controls
  toggleCornerNumbers() {
    this.config.showCornerNumbers = !this.config.showCornerNumbers;
  }

  toggleQuote() {
    this.config.showQuote = !this.config.showQuote;
  }

  togglePersonality() {
    this.config.showPersonality = !this.config.showPersonality;
  }

  changeAccentColor(color: string) {
    this.config.accentColor = color;
  }

  changeCornerNumber(number: string) {
    this.config.cornerNumber = number;
  }
}
