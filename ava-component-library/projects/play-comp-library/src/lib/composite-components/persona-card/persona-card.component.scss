.persona-card {
  --accent-color: #2196F3;
  --card-bg: #FFFFFF;
  --stripe-color: rgba(33, 150, 243, 0.1);
  --border-color: var(--accent-color);
  --text-primary: #333333;
  --text-secondary: #666666;
  --quote-bg: #F8F9FA;

  position: relative;
  width: 400px;
  background: var(--card-bg);
  border: 2px solid var(--border-color);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
  }

  // Corner Numbers
  .corner-numbers {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: 2;
  }

  .corner-number {
    position: absolute;
    background: var(--accent-color);
    color: white;
    font-weight: bold;
    font-size: 14px;
    padding: 4px 8px;
    border-radius: 4px;
    min-width: 24px;
    text-align: center;

    &--top-left {
      top: 12px;
      left: 12px;
    }

    &--bottom-left {
      bottom: 12px;
      left: 12px;
    }

    &--bottom-right {
      bottom: 12px;
      right: 12px;
    }
  }

  // Diagonal Stripes Background
  .diagonal-stripes {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: repeating-linear-gradient(
      45deg,
      transparent,
      transparent 8px,
      var(--stripe-color) 8px,
      var(--stripe-color) 16px
    );
    z-index: 1;
  }

  // Main Content
  .card-content {
    position: relative;
    z-index: 3;
    padding: 32px 24px 24px;
    background: var(--card-bg);
    margin: 16px;
    border-radius: 12px;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
  }

  // Avatar Section
  .avatar-section {
    display: flex;
    justify-content: center;
    margin-bottom: 8px;

    .persona-avatar {
      cursor: pointer;
      transition: transform 0.2s ease;

      &:hover {
        transform: scale(1.05);
      }
    }
  }

  // Title Section
  .title-section {
    text-align: center;
    margin-bottom: 8px;
  }

  .persona-title {
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    color: var(--accent-color);
    text-align: center;
  }

  // Information Grid
  .info-grid {
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 12px;
  }

  .info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #E0E0E0;

    &:last-child {
      border-bottom: none;
    }

    &--highlighted {
      background: rgba(var(--accent-color), 0.05);
      padding: 8px 12px;
      border-radius: 6px;
      border-bottom: none;
    }
  }

  .info-label {
    font-weight: 500;
    color: var(--text-primary);
    font-size: 14px;
  }

  .info-value {
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 14px;
  }

  // Quote Section
  .quote-section {
    width: 100%;
    background: var(--quote-bg);
    border-radius: 8px;
    padding: 16px;
    border-left: 4px solid var(--accent-color);
    display: flex;
    gap: 12px;
    align-items: flex-start;
  }

  .quote-icon {
    flex-shrink: 0;
    margin-top: 2px;
  }

  .quote-text {
    margin: 0;
    font-style: italic;
    color: var(--text-secondary);
    line-height: 1.5;
    font-size: 14px;
  }

  // Personality Section
  .personality-section {
    width: 100%;
    text-align: center;
  }

  .personality-title {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-primary);
  }

  .personality-tags {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
  }

  // Responsive Design
  @media (max-width: 480px) {
    width: 100%;
    max-width: 360px;

    .card-content {
      margin: 12px;
      padding: 24px 16px 16px;
      gap: 16px;
    }

    .persona-title {
      font-size: 20px;
    }

    .corner-number {
      font-size: 12px;
      padding: 3px 6px;
    }
  }
}
