import { ChangeDetectionStrategy, Component, Input, Output, EventEmitter } from '@angular/core';
import { CommonModule } from '@angular/common';
import { CardComponent } from '../../components/card/card.component';
import { CardHeaderComponent } from '../../components/card/card-header/card-header.component';
import { CardContentComponent } from '../../components/card/card-content/card-content.component';
import { CardFooterComponent } from '../../components/card/card-footer/card-footer.component';
import { AvatarsComponent } from '../../components/avatars/avatars.component';
import { AvaTagComponent } from '../../components/tags/tags.component';
import { IconComponent } from '../../components/icon/icon.component';

export interface PersonaProfile {
  id: string;
  name: string;
  title: string;
  avatar: string;
  age: number;
  education: string;
  status: string;
  location: string;
  techLiteracy: string;
  quote: string;
  personality: string[];
}

export interface PersonaConfig {
  showCornerNumbers?: boolean;
  cornerNumber?: string;
  showQuote?: boolean;
  showPersonality?: boolean;
  accentColor?: string;
}

@Component({
  selector: 'ava-persona-card',
  standalone: true,
  imports: [
    CommonModule,
    CardComponent,
    CardHeaderComponent,
    CardContentComponent,
    CardFooterComponent,
    AvatarsComponent,
    AvaTagComponent,
    IconComponent
  ],
  templateUrl: './persona-card.component.html',
  styleUrl: './persona-card.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class PersonaCardComponent {
  @Input() profile: PersonaProfile | null = null;
  @Input() config: PersonaConfig = {
    showCornerNumbers: true,
    cornerNumber: '24',
    showQuote: true,
    showPersonality: true,
    accentColor: '#2196F3'
  };

  @Output() profileClick = new EventEmitter<string>();
  @Output() avatarClick = new EventEmitter<string>();

  onProfileClick() {
    if (this.profile) {
      this.profileClick.emit(this.profile.id);
    }
  }

  onAvatarClick() {
    if (this.profile) {
      this.avatarClick.emit(this.profile.id);
    }
  }

  get personalityTags() {
    return this.profile?.personality || [];
  }

  get infoItems() {
    if (!this.profile) return [];
    
    return [
      { label: 'Age', value: this.profile.age.toString() },
      { label: 'Education', value: this.profile.education },
      { label: 'Status', value: this.profile.status },
      { label: 'Location', value: this.profile.location },
      { label: 'Tech Literacy', value: this.profile.techLiteracy }
    ];
  }
}
