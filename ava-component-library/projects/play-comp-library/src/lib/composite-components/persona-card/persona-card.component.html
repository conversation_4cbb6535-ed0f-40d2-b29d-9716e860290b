<div class="persona-card" [style.--accent-color]="config.accentColor">
  <!-- Corner Numbers -->
  <div *ngIf="config.showCornerNumbers" class="corner-numbers">
    <div class="corner-number corner-number--top-left">{{ config.cornerNumber }}</div>
    <div class="corner-number corner-number--bottom-left">{{ config.cornerNumber }}</div>
    <div class="corner-number corner-number--bottom-right">{{ config.cornerNumber }}</div>
  </div>

  <!-- Diagonal Stripes Background -->
  <div class="diagonal-stripes"></div>

  <!-- Card Content -->
  <div class="card-content">
    <!-- Avatar Section -->
    <div class="avatar-section">
      <ava-avatars
        [imageUrl]="profile?.avatar || ''"
        [profileText]="profile?.name || ''"
        size="large"
        (click)="onAvatarClick()"
        class="persona-avatar"
      >
      </ava-avatars>
    </div>

    <!-- Title -->
    <div class="title-section">
      <h2 class="persona-title">{{ profile?.title || 'Unknown Title' }}</h2>
    </div>

    <!-- Information Grid -->
    <div class="info-grid">
      <div 
        *ngFor="let item of infoItems" 
        class="info-item"
        [class.info-item--highlighted]="item.label === 'Status'"
      >
        <span class="info-label">{{ item.label }}</span>
        <span class="info-value">{{ item.value }}</span>
      </div>
    </div>

    <!-- Quote Section -->
    <div *ngIf="config.showQuote && profile?.quote" class="quote-section">
      <div class="quote-icon">
        <ava-icon iconName="quote" [iconSize]="24" iconColor="var(--accent-color)"></ava-icon>
      </div>
      <p class="quote-text">{{ profile.quote }}</p>
    </div>

    <!-- Personality Section -->
    <div *ngIf="config.showPersonality && personalityTags.length" class="personality-section">
      <h3 class="personality-title">Personality</h3>
      <div class="personality-tags">
        <ava-tag
          *ngFor="let trait of personalityTags"
          [label]="trait"
          color="primary"
          variant="outlined"
          [pill]="true"
          size="sm"
        >
        </ava-tag>
      </div>
    </div>
  </div>
</div>
