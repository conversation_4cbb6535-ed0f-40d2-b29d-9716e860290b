import {
    ApplicationRef,
    ComponentRef,
    EmbeddedViewRef,
    Injectable,
    Injector,
    Type,
    createComponent,
    EnvironmentInjector
} from '@angular/core';
import { DialogContainerComponent } from './dialog-container/dialog-container.component'
import { SuccessComponent } from './success/success.component';
import { ErrorComponent } from './error/error.component';
import { WarningComponent } from './warning/warning.component';
import { InfoComponent } from './info/info.component';
import { ConfirmationComponent } from './confirmation/confirmation.component';
import { LoadingComponent } from './loading/loading.component';
import { CustomComponent } from './custom/custom.component';

// Dialog Types and Interfaces
export interface DialogConfig {
    title?: string;
    message?: string;
    icon?: string;
    iconColor?: string;
    iconSize?: number;
    showCloseButton?: boolean;
    backdrop?: boolean;
    width?: string;
    height?: string;
    data?: any;
}

export interface ErrorDialogConfig extends DialogConfig {
    showRetryButton?: boolean;
    retryButtonText?: string;
    closeButtonText?: string;
}

export interface WarningDialogConfig extends DialogConfig {
    showProceedButton?: boolean;
    proceedButtonText?: string;
    showCancelButton?: boolean;
    cancelButtonText?: string;
}

export interface InfoDialogConfig extends DialogConfig {
    showOkButton?: boolean;
    okButtonText?: string;
    showLearnMoreButton?: boolean;
    learnMoreButtonText?: string;
}

export interface ConfirmationDialogConfig extends DialogConfig {
    confirmButtonText?: string;
    cancelButtonText?: string;
    confirmButtonVariant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
    cancelButtonVariant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
    destructive?: boolean;
}

export interface LoadingDialogConfig extends DialogConfig {
    progress?: number;
    showProgress?: boolean;
    showCancelButton?: boolean;
    cancelButtonText?: string;
    spinnerColor?: string;
    indeterminate?: boolean;
}

export interface CustomDialogConfig extends DialogConfig {
    buttons?: DialogButton[];
    variant?: 'default' | 'success' | 'error' | 'warning' | 'info';
    customContent?: string;
    showIcon?: boolean;
    showTitle?: boolean;
    showMessage?: boolean;
}

export interface DialogButton {
    label: string;
    variant?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger';
    action?: string;
    disabled?: boolean;
}

export interface DialogResult {
    action?: string;
    data?: any;
    confirmed?: boolean;
}

export type DialogType = 'success' | 'error' | 'warning' | 'info' | 'confirmation' | 'loading' | 'custom';


@Injectable({ providedIn: 'root' })
export class DialogService {
    private dialogRef!: ComponentRef<DialogContainerComponent>;
    constructor(
        private appRef: ApplicationRef,
        private injector: Injector,
        private envInjector: EnvironmentInjector
    ) { }

    open<T extends object>(component: Type<T>, data?: Partial<T>): Promise<any> {
        // Prevent body scrolling
        document.body.style.overflow = 'hidden';

        // Create container
        this.dialogRef = createComponent(DialogContainerComponent, {
            environmentInjector: this.envInjector,
            elementInjector: this.injector
        });

        this.appRef.attachView(this.dialogRef.hostView);
        const containerElem = (this.dialogRef.hostView as EmbeddedViewRef<any>)
            .rootNodes[0] as HTMLElement;
        document.body.appendChild(containerElem);

        // Create the target component dynamically inside the container
        const viewRef = this.dialogRef.instance.container.createComponent(component, {
            injector: this.injector,
            environmentInjector: this.envInjector
        });

        if (data) {
            Object.assign(viewRef.instance, data);
        }

        return new Promise(resolve => {
            // Forward close from dialog shell
            this.dialogRef.instance.closed.subscribe((result: any) => {
                this.close();
                resolve(result);
            });

            // Optionally: Listen to `closed` output from the inner component
            const inner = viewRef.instance as any;
            if (inner.closed && typeof inner.closed.subscribe === 'function') {
                inner.closed.subscribe((result: any) => {
                    this.close();
                    resolve(result);
                });
            }
        });
    }

    close() {
        if (this.dialogRef) {
            // Restore body scrolling
            document.body.style.overflow = '';

            this.appRef.detachView(this.dialogRef.hostView);
            this.dialogRef.destroy();
        }
    }
    success(config?: Partial<DialogConfig>): Promise<DialogResult> {
        return this.open(SuccessComponent, {
            title: 'Success',
            message: 'Operation completed successfully!',
            ...config
        });
    }

    error(config?: Partial<ErrorDialogConfig>): Promise<DialogResult> {
        return this.open(ErrorComponent, {
            title: 'Error',
            message: 'An error occurred. Please try again.',
            showRetryButton: false,
            showCloseButton: true,
            ...config
        });
    }

    warning(config?: Partial<WarningDialogConfig>): Promise<DialogResult> {
        return this.open(WarningComponent, {
            title: 'Warning',
            message: 'Please review the following information carefully.',
            showProceedButton: false,
            showCancelButton: true,
            ...config
        });
    }

    info(config?: Partial<InfoDialogConfig>): Promise<DialogResult> {
        return this.open(InfoComponent, {
            title: 'Information',
            message: 'Here is some important information for you.',
            showOkButton: true,
            showLearnMoreButton: false,
            ...config
        });
    }

    confirmation(config?: Partial<ConfirmationDialogConfig>): Promise<DialogResult> {
        return this.open(ConfirmationComponent, {
            title: 'Confirm Action',
            message: 'Are you sure you want to proceed with this action?',
            confirmButtonText: 'Confirm',
            cancelButtonText: 'Cancel',
            confirmButtonVariant: 'primary',
            cancelButtonVariant: 'secondary',
            destructive: false,
            ...config
        });
    }

    loading(config?: Partial<LoadingDialogConfig>): Promise<DialogResult> {
        return this.open(LoadingComponent, {
            title: 'Loading...',
            message: 'Please wait while we process your request.',
            showProgress: false,
            progress: 0,
            showCancelButton: false,
            indeterminate: true,
            ...config
        });
    }

    custom(config?: Partial<CustomDialogConfig>): Promise<DialogResult> {
        return this.open(CustomComponent, {
            title: 'Dialog',
            message: '',
            variant: 'default',
            showIcon: true,
            showTitle: true,
            showMessage: true,
            buttons: [],
            ...config
        });
    }
}
